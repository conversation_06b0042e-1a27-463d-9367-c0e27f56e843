<template>
    <div class="select_friends_list">
        <div class="search_bar">
            <el-input
                @input="debounceHandleKeywordInput"
                class="search"
                type="text"
                v-model="searchValue"
                maxlength="20"
                :placeholder="lang.search"
            ></el-input>
        </div>
        <div class="avatars_list" v-if="avatarList.length > 0">
            <div class="selected-count">
                {{ lang.has_chosen_text }}: {{ avatarList.length }}
            </div>
            <div class="avatar-container">
                <div
                    v-for="item of avatarList"
                    :key="item.id"
                    class="avatar-item"
                    @click="deleteChoose(item.id)"
                    :title="`${lang.action_delete_text} ${item.name}`"
                >
                    <mr-avatar
                        :url="getLocalAvatar(item)"
                        :radius="30"
                    ></mr-avatar>
                    <div class="avatar-name">{{ item.name }}</div>
                    <div class="remove-btn">×</div>
                </div>
            </div>
        </div>
        <NoData v-show="renderList.length == 0"></NoData>
        <div class="options_list">
            <el-checkbox
                @change="changeSelectAll"
                class="select_all"
                :label="isSelectAll"
                v-model="isSelectAll"
                v-if="isShowSelectAll && !searchValue"
                >{{ lang.select_all }}</el-checkbox
            >
            <el-checkbox-group v-model="selectedList" class="options_list_checkbox">
                <div v-for="(item, index) of renderList" class="choose_item" :key="index">
                    <el-checkbox :label="item.id" :disabled="item.disabled" class="item_checkbox">
                        <mr-avatar :url="getLocalAvatar(item)" :key="item.avatar"></mr-avatar>
                        <p class="contact-name">{{ item.name }}</p>
                    </el-checkbox>
                </div>
            </el-checkbox-group>
        </div>
    </div>
</template>
<script>
import base from "../lib/base";
import Tool from "@/common/tool";
import NoData from "../MRComponents/noData.vue";
import { getLocalAvatar } from "../lib/common_base";
export default {
    mixins: [base],
    name: "ContactSelectList",
    model: {
        prop: "value",
        event: "change",
    },
    props: {
        options: {
            type: Array,
            default: () => {
                return [];
            },
        },
        isShowSelectAll: {
            type: Boolean,
            default: false,
        },
        value: {
            type: Array,
            default: () => {
                return [];
            },
        },
    },
    components: {
        NoData,
    },
    computed: {
        renderList() {
            if (this.searchValue || this.regList.length > 0) {
                return this.regList;
            } else {
                return this.options;
            }
        },
        avatarList() {
            let arr = [];
            this.options.forEach((item) => {
                if (this.selectedList.includes(item.id)) {
                    arr.push(item);
                }
            });
            return arr;
        },
    },
    watch: {
        selectedList: {
            handler(val) {
                this.$emit("change", val);
            },
        },
    },
    data() {
        return {
            getLocalAvatar,
            searchValue: "",
            selectedList: [],
            regList: [],
            isSelectAll: false,
        };
    },
    created() {},
    methods: {
        filterDataByName(data, pattern) {
            // 使用正则表达式筛选数据
            const filteredData = data.filter((item) => {
                // 使用正则表达式进行匹配
                const regex = new RegExp(pattern, "ig");
                return item.name && regex.test(item.name);
            });
            return filteredData;
        },
        debounceHandleKeywordInput: Tool.debounce(function () {
            const keyword = this.searchValue;
            const filteredData = this.filterDataByName(this.options, keyword);
            this.regList = filteredData;
        }, 300),
        deleteChoose(id) {
            this.selectedList = this.selectedList.filter((item) => item !== id);
        },
        changeSelectAll() {
            if (this.isSelectAll) {
                this.selectedList = this.options.filter((item) => !item.disabled).map((item) => item.id);
            } else {
                this.selectedList = [];
            }
        },
    },
};
</script>
<style lang="scss" scoped>
.select_friends_list {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background: #ffffff;
    border-radius: 8px;
    min-width: 0; // 确保可以收缩

    .search_bar {
        display: flex;
        padding: 16px;
        background: #f8f9fa;
        border-radius: 8px 8px 0 0;
        border-bottom: 1px solid #e9ecef;

        .search {
            min-width: 150px;
            width: auto;
            flex: 1;

            ::v-deep {
                .el-input__inner {
                    border: 1px solid #dee2e6;
                    border-radius: 20px;
                    padding: 0 16px;
                    height: 36px;
                    line-height: 36px;
                    background: #ffffff;
                    transition: all 0.3s ease;

                    &:focus {
                        border-color: #779a98;
                        box-shadow: 0 0 0 2px rgba(119, 154, 152, 0.1);
                    }

                    &::placeholder {
                        color: #6c757d;
                    }
                }
            }
        }
    }

    .avatars_list {
        max-height: 140px;
        overflow: auto;
        padding: 16px;
        background: #f8f9fa;
        border-bottom: 1px solid #e9ecef;

        .selected-count {
            font-size: 12px;
            color: #779a98;
            font-weight: 600;
            margin-bottom: 12px;
            padding: 4px 8px;
            background: rgba(119, 154, 152, 0.1);
            border-radius: 12px;
            display: inline-block;
        }

        .avatar-container {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
        }

        .avatar-item {
            cursor: pointer;
            padding: 8px;
            border-radius: 8px;
            transition: all 0.2s ease;
            position: relative;
            background: #ffffff;
            border: 1px solid #e9ecef;
            text-align: center;
            width: 70px;
            display: flex;
            flex-direction: column;
            align-items: center;
            &:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(119, 154, 152, 0.15);
                border-color: #779a98;

                .remove-btn {
                    opacity: 1;
                }
            }

            .avatar-name {
                font-size: 11px;
                color: #495057;
                margin-top: 4px;
                line-height: 1.2;
                max-width: 60px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .remove-btn {
                position: absolute;
                top: -4px;
                right: -4px;
                width: 18px;
                height: 18px;
                background: #dc3545;
                color: white;
                border-radius: 50%;
                font-size: 14px;
                line-height: 18px;
                text-align: center;
                opacity: 0;
                transition: all 0.2s ease;
                font-weight: bold;

                &:hover {
                    background: #c82333;
                    transform: scale(1.1);
                }
            }
        }
    }

    .options_list {
        flex: 1;
        overflow: auto;
        padding: 16px;
        min-width: 0; // 确保可以收缩

        .select_all {
            margin-bottom: 12px;
            margin-top: 0;
            padding: 8px 12px;
            background: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #e9ecef;
            transition: all 0.2s ease;

            &:hover {
                background: #e9ecef;
            }

            ::v-deep {
                .el-checkbox__input.is-checked .el-checkbox__inner {
                    background-color: #779a98;
                    border-color: #779a98;
                }

                .el-checkbox__inner:hover {
                    border-color: #779a98;
                }
            }
        }

        .options_list_checkbox {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            min-width: 0; // 确保grid容器可以收缩
        }

        .choose_item {
            margin: 0;
            overflow: hidden; // 改为hidden防止溢出
            min-width: 0; // 确保grid项目可以收缩

            .item_checkbox {
                margin-right: 0;
                width: 100%;
                min-width: 0; // 确保可以收缩
                display: flex;
                align-items: center;
                padding: 10px;
                border-radius: 8px;
                border: 1px solid #e9ecef;
                background: #ffffff;
                transition: all 0.2s ease;
                position: relative;
                z-index: 1;
                box-sizing: border-box; // 确保padding不会导致溢出

                &:hover {
                    border-color: #779a98;
                    background: rgba(119, 154, 152, 0.05);
                    box-shadow: 0 2px 8px rgba(119, 154, 152, 0.15);
                    z-index: 2;
                }

                &.is-disabled {
                    background: #f8f9fa;
                    opacity: 0.6;
                    cursor: not-allowed;

                    &:hover {
                        box-shadow: none;
                        border-color: #e9ecef;
                        background: #f8f9fa;
                        z-index: 1;
                    }
                }

                ::v-deep {
                    .el-checkbox__input.is-checked .el-checkbox__inner {
                        background-color: #779a98;
                        border-color: #779a98;
                    }

                    .el-checkbox__inner:hover {
                        border-color: #779a98;
                    }

                    .el-checkbox__label {
                        flex: 1;
                        min-width: 0;
                        display: flex;
                        align-items: center;
                        margin-left: 8px;
                    }
                }

                .contact-name {
                    font-size: 14px;
                    flex: 1;
                    line-height: 1.4;
                    padding: 0 0 0 12px;
                    margin: 0;
                    color: #495057;
                    font-weight: 500;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
            }
        }
    }

    // 滚动条样式优化
    ::-webkit-scrollbar {
        width: 6px;
        height: 6px;
    }

    ::-webkit-scrollbar-track {
        background: #f1f3f4;
        border-radius: 3px;
    }

    ::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;

        &:hover {
            background: #a8a8a8;
        }
    }

    // 响应式设计
    @media (max-width: 768px) {
        .search_bar {
            padding: 12px;
        }

        .avatars_list {
            padding: 8px 12px;
        }

        .options_list {
            padding: 12px;

            .options_list_checkbox {
                grid-template-columns: 1fr;
            }

            .item_checkbox {
                padding: 10px;
            }
        }
    }

    // 加载动画
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .choose_item {
        animation: fadeInUp 0.3s ease-out;

        &:nth-child(even) {
            animation-delay: 0.05s;
        }
    }

    // 空状态样式
    .no-data {
        text-align: center;
        padding: 40px 20px;
        color: #6c757d;

        .no-data-icon {
            font-size: 48px;
            color: #dee2e6;
            margin-bottom: 16px;
        }

        .no-data-text {
            font-size: 14px;
            line-height: 1.5;
        }
    }
}
</style>
