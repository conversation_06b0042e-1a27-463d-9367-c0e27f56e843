<template>
<div>
	<CommonDialog
      class="edit_groupset_page"
      :title="title"
      :show.sync="visible"
      :close-on-click-modal="false"
      :append-to-body="true"
      min-width="700px"
      :modal="false"
      v-loading="loading"
      @closed="back"
      :footShow = "false"
      >
      	<div class="container">
           <template v-if="step==1">
                <div class="step_1" @keyup.enter="editGroupSubmit">
                    <p class="modify_tip">{{lang.group_set_name}}</p>
                    <el-input type="text"  class="common_input" v-model="subject" maxlength="50" ref="subject"></el-input>
                </div>
            </template>
            <template v-else>
                <div class="switch_list">
                    <el-button v-if="listType==1" @click="toggleList(2)" type="primary" size="small">{{lang.select_from_group_list}}</el-button>
                    <el-button v-if="listType==2" @click="toggleList(1)" type="primary" size="small">{{lang.select_from_friend_list}}</el-button>
                </div>
                <ContactSelectList :options="groupListOptions" v-model="currentSelectGroupList" class="contact-select-list" v-show="listType==2" key="group"></ContactSelectList>
                <ContactSelectList :options="friendListOptions" v-model="currentSelectFriendList" class="contact-select-list" v-show="listType==1" key="friend"></ContactSelectList>
            </template>
        </div>
      	<div class="clearfix btns">
      		<template v-if="type==1">
      			<el-button v-show="step==1" :disabled="subject.length==0" type="primary" size="medium" class="fr" @click="editGroupSubmit">{{lang.next_step_text}}</el-button>
      			<el-button v-show="step==2||step==3" type="primary" size="medium" class="fl" @click="backHandler">{{lang.back_button}}</el-button>
         		<el-button v-show="step==2" :disabled="!enableNext" :class="enableNext" type="primary" size="medium" class="fr" @click="editGroupSubmit">{{lang.next_step_text}}</el-button>
            </template>
            <template v-else>
                <el-button v-show="type!=4&&step==2" type="primary" size="medium" class="fl" @click="backHandler">{{lang.back_button}}</el-button>
            	<el-button v-show="type!=4" :disabled="!enableNext" type="primary" size="medium" class="fr" @click="editGroupSubmit">{{lang.save_txt}}</el-button>
            </template>
     	</div>
  	</CommonDialog>
</div>

</template>
<script>
import groupsetTool from '../lib/groupsetTool'
import base from '../lib/base'
import {parseSingleChat,getLocalAvatar} from '../lib/common_base'
import ContactSelectList from '../components/contactSelectList'
import CommonDialog from "../MRComponents/commonDialog.vue"; //3rd change

export default {
    mixins: [base,groupsetTool],
    name: 'editGroupset',
    components: {
        ContactSelectList,
        CommonDialog
    },
    data(){
        return {
            getLocalAvatar,
            visible: false,
            title:'',
            id:this.$route.params.groupset_id,
            groupList:this.$store.state.groupList,
            friendList:this.$store.state.friendList.list,
            chatList:this.$store.state.chatList,
            type:this.$route.params.type,//1新增,2编辑,3删除
            step:this.$route.params.step,//1,群落名,2成员列表
            loading:false,
            subject:'',
            listType:2,
            groupListOptions:[],
            friendListOptions:[],
            currentSelectGroupList:[],
            currentSelectFriendList:[],
        }
    },
    computed:{
        enableNext(){
            return this.currentSelectGroupList.length + this.currentSelectFriendList.length>0;
        },
        details(){
            return this.$store.state.groupset.details
        },
        currentGroupsetGroupList(){
            let arr = []
            this.details[this.id]&&this.details[this.id].groupList.forEach(item=>{
                if(item.type !== this.systemConfig.ConversationConfig.type.Single){
                    arr.push(item)
                }
            })
            return arr
        },
        currentGroupsetFriendList(){
            let arr = []
            this.details[this.id]&&this.details[this.id].groupList.forEach(item=>{
                if(item.type === this.systemConfig.ConversationConfig.type.Single){
                    arr.push({...item,nickname:item.subject})
                }
            })
            return arr
        },
        remarkMap(){
            return this.$store.state.friendList.remarkMap
        }
    },
    mounted(){
        this.$nextTick(() => {
            this.visible = true;
        });
        let pureFriendList=[]
        for(let friend of this.friendList){
            let item=Object.assign({},friend);
            if (item.alias) {
                item.nickname=item.alias;
            }
            if (item.service_type==0&&item.user_status!=this.systemConfig.userStatus.Destroy) {
                pureFriendList.push(item)
            }
        }
        this.friendList=pureFriendList;
        this.setDialogTitle();
        if(Number(this.type) === 2){ //编辑
            this.initAddGroupListOptions()
            this.initAddFriendListOptions()
        }else if(Number(this.type) === 3){ //删除
            this.initDeleteGroupListOptions()
            this.initDeleteFriendListOptions()
        }

    },
    methods:{
        initAddGroupListOptions(){
            let arr=[]
            this.groupList.forEach(item=>{
                arr.push({
                    name:item.subject,
                    avatar:item.avatar,
                    id:item.id,
                    disabled:this.currentGroupsetGroupList.some(group=>{
                        return Number(group.id)===Number(item.id)
                    })
                })
            })
            console.log(this.groupListOptions,'this.groupListOptions')
            this.groupListOptions = arr
        },
        initAddFriendListOptions(){
            let arr=[]
            this.friendList.forEach(item=>{
                let nickname = item.nickname
                if (this.remarkMap[item.id]) {
                    nickname=this.remarkMap[item.id]
                }
                arr.push({
                    name:nickname,
                    avatar:item.avatar,
                    id:item.id,
                    disabled:this.currentGroupsetFriendList.some(friend=>{
                        return Number(friend.id)===Number(item.id)
                    })
                })
            })
            console.log(this.friendList,this.friendListOptions,'this.friendListOptions')
            this.friendListOptions = arr
        },
        initDeleteGroupListOptions(){
            let arr=[]
            this.currentGroupsetGroupList.forEach(item=>{
                arr.push({
                    name:item.subject,
                    avatar:item.avatar,
                    id:item.id,
                })
            })
            this.groupListOptions = arr
        },
        initDeleteFriendListOptions(){
            let arr=[]
            this.currentGroupsetFriendList.forEach(item=>{
                let nickname = item.nickname
                if (this.remarkMap[item.id]) {
                    nickname=this.remarkMap[item.id]
                }
                arr.push({
                    name:nickname,
                    avatar:item.avatar,
                    id:item.id,
                })
            })
            console.log(this.friendListOptions,'this.friendListOptions')
            this.friendListOptions = arr
        },
        setDialogTitle(){
            if (this.type==1) {
                this.title=this.lang.create_group_set;
                if (this.step==2) {
                    this.title=this.lang.groupset_member_title
                }
            } else if (this.type==2) {
                this.title=this.lang.groupset_member_title
            } else if (this.type==3) {
                this.title=this.lang.groupset_delete_attendee;
            }
        },
        editGroupSubmit(){
            if (this.type==1) {
                if (this.step==1) {
                    if (this.subject!='') {
                        if (this.groupsetNameExit()) {
                            return ;
                        }
                        this.step=2;
                        this.setDialogTitle()
                        this.initAddGroupListOptions()
                        this.initAddFriendListOptions()
                        this.currentSelectGroupList = []
                        this.currentSelectFriendList = []
                        this.setDialogTitle()
                    }
                    return ;
                }
            }
            this.doGroupsetAction()
        },
        doGroupsetAction(){
            if (this.loading) {
                return ;
            }
            this.loading=true;
            let list=[]
            let json={}
            if (this.type==1) {
                json={
                    subject:this.subject,
                    uidList:this.currentSelectFriendList,
                    gidList:this.currentSelectGroupList,
                }
                console.log('add_groupset',json)
                window.main_screen.createGroupset(json,(data)=>{
                    this.loading=false;
                    console.log(data)
                    if (data.error_code==0) {
                        data.data.type=3;
                        this.$store.commit('groupset/appendToGroupsetList',data.data);
                        this.back();
                        setTimeout(()=>{
                            this.$store.commit('groupset/setCurrentGroupset',data.data)
                            this.$router.replace('/index/chat_window/0/groupset_wall/'+data.data.id)
                        },200)
                    }
                })
            } else if (this.type==2) {
                json={
                    groupSetID:this.id,
                    uidList:this.currentSelectFriendList,
                    gidList:this.currentSelectGroupList
                }
                console.log('addMember',json)
                window.main_screen.addGroupsetMember(json,(data)=>{
                    console.log(data)
                    if (data.error_code==0) {
                        data.data.type=3;
                        data.data.groupList=parseSingleChat(data.data.groupList)
                        this.$store.commit('groupset/updateGroupsetDetail',data.data)
                        this.$store.commit('groupset/deleteGroupsetWall',this.id)
                        this.$root.eventBus.$emit('refreshGroupsetWall',this.id)
                        this.back()
                    }

                })
            } else if (this.type==3) {
                json={
                    groupSetID:this.id,
                    groupIDList:this.currentSelectGroupList.concat(this.currentSelectFriendList)
                }
                window.main_screen.removeGroupsetMember(json,(data)=>{
                    if (data.error_code==0) {
                        data.data.type=3;
                        data.data.groupList=parseSingleChat(data.data.groupList)
                        this.$store.commit('groupset/updateGroupsetDetail',data.data);
                        this.$store.commit('groupset/deleteGroupsetWall',this.id)
                        this.$root.eventBus.$emit('refreshGroupsetWall',this.id)
                        this.back();
                    }
                })
            }
        },
        backHandler(){
            if (this.type==1) {
                if (this.step==1) {
                    this.back();
                } else if (this.step==2) {
                    this.step=1;
                }
            } else{
                this.back()
            }
            this.setDialogTitle()
        },
        toggleList(type){
            this.listType=type;
        },
        groupsetNameExit(){
            const list =this.$store.state.groupset.list;
            for(let item of list){
                if (item.subject==this.subject) {
                    this.$message.error(this.lang.groupSetNameExists)
                    return true;
                }
            }
            return false;
        }
    }
}
</script>
<style lang="scss">
.edit_groupset_page{
    .el-dialog__body{
        display:flex;
        flex-direction: column;
        .container{
            flex:1;
            min-height: 0;
            overflow: auto;
            display:flex;
            flex-direction:column;
            .switch_list{
                padding-top: 10px;
                padding-bottom:10px;
            }
            .contact-select-list{
                flex:1
            }
            .no_data_tip{
                text-align: center;
                margin-top: 1rem;
            }
            .modify_tip{
                line-height: 2;
                font-size: 16px;
            }
            .step_1{
                margin-bottom: 20px;
                .common_input{
                    margin-bottom: 20px;
                }
            }
        }
    }
}
</style>
