// 导入 hostConfig（假设 hostConfig 是正确导出的模块）
const hostConfig = require('./hostConfig')
console.log(hostConfig)
const proxy = {
    consult: 'https://consult.mindray.com:3001',
    consult_lan: 'https://consult-lan.mindray.com:3001',
    consult_dev: 'https://consult-lan-dev.mindray.com',
    consult_test: 'https://consult-lan-test.mindray.com',
    online_dev: `https://${hostConfig.dev}`,
    online: 'https://consult.mindray.com',
    online_dev_test: 'https://consult-dev-test.mindray.com',
    online_beta: `https://${hostConfig.beta}`,
    online_mico: 'https://micoplus.mindray.com',
    mico_dev: `https://${hostConfig.ce_dev}`,
    internal_test: 'https://*************',
    nginx: 'https://*************',
    huangweilong: 'http://*************:4433',
    shu<PERSON><PERSON><PERSON><PERSON>: 'http://*************:4443',
};

// 设置代理环境，可以一键切换项目环境
let consult_env = proxy.online_dev; // 默认环境设置为 online

// 公共配置提取
const commonProxyConfig = {
    target: consult_env,
    changeOrigin: true,
    secure: false,
    headers: {
        Referer: consult_env,
    },
};

// 使用模块导出默认值
module.exports =  {
    proxyTable: [
        '/login',
        '/logout',
        '/stylesheets',
        '/CustomPortrait',
        '/history_version',
        '/socket.io',
        '/Upload',
        '/UploadConsultationFile',
        '/MultiCenter',
        '/images',
        '/query_login_config',
        '/query_register_sms_identification',
        '/query_login_sms_identification',
        '/reset_password',
        '/query_sms_identification',
        '/submit_sms_identification',
        '/query_reset_mobile_sms_identification',
        '/submit_reset_mobile_sms_identification',
        '/verify_account',
        '/modify_personal_fancy',
        '/AdminLogin',
        '/AdminRegisterApplyQuery',
        '/AdminRegisterApplyVerify',
        '/AdminUserManageQuery',
        '/AdminUserManageSetUserRole',
        '/AdminUserManageSetUserStatus',
        '/AdminUserManageResetUserPassword',
        '/AdminHospitalManageQuery',
        '/AdminHospitalManageAddHospital',
        '/AdminHospitalManageDeleteHospital',
        '/AdminHospitalManageUpdateHospital',
        '/query_hospitals',
        '/AdminUserManageSetUserHospital',
        '/autologin_encrypt',
        '/query_international_code_list',
        '/v2/api',
        '/v2/upload/liveCoverImg',
        '/v2/api/visitorToken',
        '/v2/permission',
        '/v2/server/info',
        '/buildTime',
        '/qclive'
    ].reduce((config, path) => {
        config[path] = commonProxyConfig;
        return config;
    }, {}),
    env: consult_env,  // 传递当前的环境配置
};
