import Tool from '@/common/tool.js'
import libraryService from "../service/libraryService.js";
import service from "../service/service.js";
import { cloneDeep, sortBy, uniqBy} from "lodash"
import { Toast, Swipe, SwipeItem, List, Field, PullRefresh, Loading } from "vant";
export default {
    data(){
        return {
            filters: {}, 
            default_filters: {
                focus_input: false,
                searchInputKey: "",
                isSearchLoading: false,
                isShowCondition: false, //是否显示过滤条件
                per_page: 10,
                condition: {
                    category: [],
                    tag: [],
                },
                hot_types: [
                    // { icon: "icon-new-popular-post", is_active: "is_active", cn: "新热推荐",en:"What's New",key:'new_hot_post',id:0 },
                    // { icon: "icon-linchuang", is_active: "", cn: "临床美图",en:'Image Gallery',key:'clinical_beauty_map_post' ,id:0},
                    // { icon: "icon-jiaopei", is_active: "", cn: "名师讲堂",en:'Expert Lectures',key:'famous_teacher_post' ,id:0},
                    // { icon: "icon-guide-post", is_active: "", cn: "操作指导",en:'Operation Guide',key:'operation_guidance' ,id:0},
                    // { icon: "icon-more_popular", is_active: "", cn: "瑞影爆品",en:'Products' ,key:'explosive_product_post',id:0},
                    // { icon: "icon-doctor_home", is_active: "", cn: "医工之家",en:'Customer Service',key:'medical_workers_home_post' ,id:0},
                    // { icon: "icon-medical_tecn", is_active: "", cn: "功能技术",en:'Technologies',key:'functional_technology_post',id:0 },
                    // { icon: "icon-animal-post", is_active: "", cn: "Vetus Club",en:'Vetus Club' ,key:'vetus_club_post',id:0},
                ],
                swipe_posts: [],
            },
        }
    },
    methods:{
        async getLibraryToken(external_token){
            const res=await libraryService.getLibraryToken({
                request_type:'post',
                external_token: external_token
            });
            if(res && res.data &&res.data.token){
                this.$store.commit('libraryData/updateLibraryData',{library_token: res.data.token})
            }
        },
        async  getExternalToken(external_token){
            const res=await service.getExternalToken({type:'library'});
            if (res && res.data && res.data.error_code===0) {
                return res.data.data.external_token;
            }else{
                return ''
            }
        },
        getListDataFromStore(key){
            if(this.$store.state.libraryData&&this.$store.state.libraryData[key]&&(Object.keys(this.$store.state.libraryData[key]).length>0)){
                return cloneDeep(this.$store.state.libraryData[key])
            }
            return false;
        },
         
        async getPostSlideImages() {
            return new Promise((resolve, reject) => {
                libraryService.getPostSlideImages({}).then(async (res) => {
                    if (res && res.status == 200) {
                        if (res.data && res.data.length > 0) {
                            let d = res.data.reduce((h, v) => {
                                v.url = v.thumbnail
                                    .split("<img")[1]
                                    .split("src=")[1]
                                    .split("class=")[0]
                                    .split('"')
                                    .join("");
                                h.push(v);
                                return h;
                            }, []);
                            resolve(d);
                        } else {
                            resolve([]);
                        }
                    } else {
                        Toast(this.lang.operate_err);
                        resolve([]);
                    }
                });
            });
        },
        initTags(tags){
            let hot_types = this.filters.hot_types.reduce((h, v) => {
                h.push(this.isCE? v.en : v.cn)
                return h;
            }, []);
            let new_tags = tags.reduce((h, v) => {
                v['in_pop'] = true;
                h.push(v)
                if (hot_types.indexOf(v.name)>=0) {
                    this.filters.hot_types = this.filters.hot_types.reduce((hd, item) => {
                        let name = this.isCE? item.en : item.cn
                        if(v.name == name && item.key!='new_hot_post'){
                            item.id = v.id
                            v['in_pop'] = false;
                        }
                        hd.push(item)
                        return hd;
                    }, []);
                   
                }
                return h;
            }, []);
            return new_tags;
        },
        async getAllTags() {
            return new Promise((resolve, reject) => {
                libraryService.getTags({ per_page: 100 }).then(async (res) => {
                    if (res && res.status == 200) {
                        resolve(this.initTags(res.data));
                    } else {
                        Toast(this.lang.operate_err);
                        resolve([]);
                    }
                });
            });
        },
        async getPostTags() {
            return new Promise((resolve, reject) => {
                libraryService.getPostTags({  }).then(async (res) => {
                    if (res && res.status == 200) {
                        resolve(this.initTags(res.data));
                    } else {
                        Toast(this.lang.operate_err);
                        resolve([]);
                    }
                });
            });
        },
        async search(clear_post) {
            if(!clear_post&&this.posts.length>0&&this.total>0&&this.total<=this.posts.length ){
                this.filters.isSearchLoading = false;
                return 
            }
            return new Promise(async (resolve, reject) => {
                if (clear_post) {
                    // this.$store.commit('libraryData/updateLibraryData',{posts:[],total:0})
                    this.filters.page = 1;
                    let new_posts = await this.getPostsByCondition(clear_post);
                    let posts = new_posts.data;
                    this.$store.commit('libraryData/updateLibraryData',{posts,total:new_posts.total})
                } else {
                    if (this.posts.length < this.total) {
                        this.filters.page = Math.floor(this.posts.length / this.filters.per_page) + 1;
                        let new_posts = await this.getPostsByCondition(clear_post);
                        let posts = uniqBy([...this.posts, ...new_posts.data], "id");
                        this.$store.commit('libraryData/updateLibraryData',{posts,total:new_posts.total})
                    }
                }
                this.filters.isSearchLoading = false;
                resolve([])
                return 
            })
        },
        async getPostsByCondition(clear_post = true) {
            let that = this;
            return new Promise(async (resolve, reject) => {
                let timer = setTimeout(() => {
                    Toast(that.lang.requestTimeout);
                    that.filters.isSearchLoading = false;
                    resolve({ data: [], total: 0 });
                }, 30 * 1000);
                let params = {request_type:'post',  per_page: that.filters.per_page, page: that.filters.page };
                if(that.filters.condition){
                    let tags = that.filters.condition.tag.reduce((h, v) => {
                        if (v.is_checked) {
                            h.push(v.id);
                        }
                        return h;
                    }, []);
                    if(that.filters.hot_types){
                        that.filters.hot_types.forEach((item, i) => {
                            if (item.is_active && item.id>0) {
                                tags.push(item.id)
                            }
                        });
                    }
                    if (tags.length > 0) {
                        params['tag_ids'] = tags//.join(",");
                    }
                }
                
                // let categories = this.filters.condition.category.reduce((h, v) => {
                //     if (v.is_checked) {
                //         h.push(v.id);
                //     }
                //     return h;
                // }, []);
                
                // if (categories.length > 0) {
                //     params.categories = categories.join(",");
                // }
                if (that.filters.searchInputKey) {
                    params.search = that.filters.searchInputKey;
                    params.search_columns=['post_title']
                }
                
                libraryService.searchPosts(params).then(async (res) => {
                    that.filters.isSearchLoading = false;
                    clearTimeout(timer);
                    if (res.status == 200) {
                        resolve({ data: res.data['posts'], total: Number(res.data['total'] || 0) });
                    } else {
                        Toast(that.lang.operate_err);
                        resolve({ data: [], total: 0 });
                    }
                });
            });
        },
        async getAllCategories() {
            return new Promise((resolve, reject) => {
                let that = this;
                libraryService.getCategories({ per_page: 100 }).then(async (res) => {
                    if (res && res.status == 200) {
                        resolve(res.data);
                    } else {
                        Toast(that.lang.operate_err);
                        resolve([]);
                    }
                });
            });
        },
        libraryDataLoad() {
            let that = this
            that.filters = cloneDeep(that.default_filters);
            (async()=>{
                if(!that.$store.state.libraryData.library_token){
                    let external_token=await that.getExternalToken();
                    if(external_token){
                        await that.getLibraryToken(external_token);
                        
                    }
                }
                let swipe_posts = await that.getPostSlideImages();
                let tags = await that.getAllTags();
                let hot_tags =  cloneDeep(that.filters.hot_types).reduce((h, item) => {
                    item.is_active =  "";
                    h.push(item)
                    return h;
                },[]);
                hot_tags[0].is_active = "is_active";
                that.$store.commit('libraryData/updateLibraryData',{swipe_posts,tags,hot_tags}) ;
                let new_hot_posts = await that.search(true);
                that.$store.commit('libraryData/updateLibraryData',{init_posts:{posts:that.$store.state.libraryData.posts,total:that.$store.state.libraryData.total}}) ;
            })()

        },

    }

}
