<template>
    <div class="library_repository_page second_level_page">
        <mrHeader>
            <template #title>
                {{ lang.mindray_library }}
            </template>
        </mrHeader>
        <div class="repository_content" v-loading="isLoading">
            <div class="search_btn">
                <van-field
                    ref="search_input"
                    clearable
                    @blur="blurInput()"
                    @focus="focusInput()"
                    @keyup.enter="searchInput"
                    @click="focusInput()"
                    v-model="filters.searchInputKey"
                >
                    <template #right-icon>
                        <template v-if="!filters.focus_input && !filters.searchInputKey">
                            <i class="iconfont icon-search svg_icon_search"> </i>
                            <div class="icon_desc">{{ lang.search }}</div>
                        </template>
                    </template>
                </van-field>
            </div>
            <van-pull-refresh v-model="isRefresh" :disabled="refresh_disable" @refresh="onRefresh" style="flex: 1">
                <div class="type" ref="main_post_content">
                    <div class="swipe_posts">
                        <van-swipe :autoplay="3000" :height="'10rem'">
                            <van-swipe-item
                                v-for="(item, i) in filters.swipe_posts"
                                :key="item.id"
                                :default="i"
                                class="swipe_posts_van"
                                @click="viewRepositoryPost({ id: item.pid })"
                            >
                                <div class="swipe_posts_item">
                                    <img v-lazy="item.url" />
                                </div>
                            </van-swipe-item>
                        </van-swipe>
                    </div>
                    <div class="hot_type">
                        <van-swipe :loop="false" :show-indicators="false" @change="handleSwipeChange">
                            <van-swipe-item v-for="i in Math.ceil(filters.hot_types.length / 8)" :key="i" :default="i">
                                <div class="row" v-for="k in 2" :key="k">
                                    <div
                                        class="hot_type_item"
                                        v-for="j in 4"
                                        :key="j"
                                        :class="[get_hot_type(i, k, j, 'is_active')]"
                                        @click="changeHotTypeByIndex(get_hot_type(i, k, j))"
                                    >
                                        <svgLoader
                                            :url="get_hot_type(i, k, j, 'icon')"
                                            customClass="library_icon_img"
                                        ></svgLoader>
                                        <div class="hot_type_name">
                                            {{ get_hot_type(i, k, j, "name") }}
                                        </div>
                                    </div>
                                </div>
                            </van-swipe-item>
                            <template #indicator>
                                <div class="custom-indicator">
                                    <span
                                        v-for="(_, index) in Math.ceil(filters.hot_types.length / 8)"
                                        :key="index"
                                        :class="{ 'line-active': index === currentActiveIndex }"
                                        class="line"
                                    />
                                </div>
                            </template>
                        </van-swipe>
                        <!-- <div class="row" v-for="i in Math.ceil(filters.hot_types.length / 4)" :key="i">
                            <div
                                class="hot_type_item"
                                v-for="j in 4"
                                :key="j"
                                :class="[get_hot_type(i, j, 'is_active')]"
                                @click="changeHotTypeByIndex(get_hot_type(i, j))"
                            >
                                <svgLoader :url="get_hot_type(i, j, 'icon')" customClass="library_icon_img"></svgLoader>
                                <div class="hot_type_name">
                                    {{ get_hot_type(i, j, "name") }}
                                </div>
                            </div>
                        </div> -->
                    </div>
                    <div class="filter_type">
                        <div class="filter_type_list" ref="filter_type_scroll">
                            <!-- <span
                                class="filter_type_item"
                                :class="{ 'is-active': item.is_checked }"
                                v-for="item in filters.condition.category"
                                :key="'category' + item.id"
                                @click="filedChange(item, 'category')"
                            >
                                {{ item.name }}</span
                            > -->
                            <span
                                class="filter_type_item"
                                v-for="item in (currentTag.length > 0 ? currentTag : filters.condition.tag).filter(
                                    (v) => v.in_pop
                                )"
                                :class="{ 'is-active': item.is_checked }"
                                :key="'tag' + item.id"
                                @click="filedChange(item, 'tag')"
                            >
                                {{ item.name }}
                            </span>
                        </div>
                        <div class="filter_type_icon">
                            <i class="icon iconfont icon-filter" @click="toggleCondition"></i>
                        </div>
                    </div>
                    <div class="result_type">
                        <div class="result_type_tips">
                            {{ lang.total_count_of_post.replace("${name}", total > 99 ? "99+" : total) }}
                        </div>
                        <div class="result_type_shuaxin" @click="handleReset()">
                            <i class="icon iconfont icon-shuaxin icon_shuxin_filter"></i>
                            {{ lang.register_reset_btn }}
                        </div>
                    </div>
                    <div class="current_post">
                        <template v-if="posts.length == 0 && !filters.isSearchLoading">
                            <div class="current_post_empty">
                                <div class="empty-icon">
                                    <i class="icon iconfont icon-no_data_post"></i>
                                </div>
                                <div>
                                    {{ lang.sorry_no_post_dat_with_tag }}
                                </div>
                            </div>
                        </template>
                        <template v-else>
                            <van-list
                                ref="loadmore"
                                :immediate-check="false"
                                @load="loadMorePosts"
                                v-model:loading="filters.isSearchLoading"
                                :finished="posts.length >= total"
                                :loading-text="lang.bottom_loading_text"
                                :class="'current_post'"
                                offset="10"
                            >
                                <div
                                    class="current_post_item"
                                    v-for="(post, i) in posts"
                                    :key="'post' + i"
                                    @click="viewRepositoryPost(post)"
                                >
                                    <div class="post_image">
                                        <img
                                            :src="getPostImage(post)"
                                            :class="{ default_image: getPostImage(post) == defaultImage }"
                                        />
                                    </div>
                                    <div class="post_title" v-html="markTitle(post.title)"></div>
                                    <div class="post_like">
                                        <i class="iconfont icon-eye_post post_like_item">{{
                                            post.post_more_details.postviews > 99
                                                ? "99+"
                                                : post.post_more_details.postviews
                                        }}</i>
                                        <i class="iconfont icon-like_post post_like_item">{{
                                            post.post_more_details.postlikes > 99
                                                ? "99+"
                                                : post.post_more_details.postlikes
                                        }}</i>
                                    </div>
                                </div>
                            </van-list>
                        </template>
                    </div>
                    <div v-if="posts.length >= total && posts.length > 0" class="no-more-text">
                        {{ lang.no_more_text }}
                    </div>
                </div>
            </van-pull-refresh>
        </div>
        <repository-fliter
            ref="case_fliter"
            :filters="filters"
            :currentTag="currentTag"
            :isShowCondition.sync="filters.isShowCondition"
            @handleback="handleback()"
        ></repository-fliter>
        <!-- <div v-for="post in posts" :key="post.id" >
            <div >
                {{ post.title.rendered }}
            </div>
            {{getImage(post)}}
            <img :src="getImage(post)" class="post_thumbnail" v-if="getImage(post)"/>
        </div> -->
        <router-view></router-view>
    </div>
</template>
<script>
import base from "../../lib/base";
import library from "../../lib/library";
import libraryService from "../../service/libraryService.js";
import service from "../../service/service.js";
import languages from "@/common/language";
import repositoryFliter from "@/module/ultrasync/components/repositoryFliter.vue";
import { cloneDeep, trim, uniqBy } from "lodash";
import Tool from "@/common/tool.js";
import svgLoader from "@/module/ultrasync/components/svgLoader.vue";
import { Toast, Swipe, SwipeItem, List, Field, PullRefresh, Loading } from "vant";
export default {
    mixins: [base, library],
    name: "repository_page",
    components: {
        repositoryFliter,
        VanSwipe: Swipe,
        VanSwipeItem: SwipeItem,
        VanList: List,
        vanField: Field,
        VanPullRefresh: PullRefresh,
        VanLoading: Loading,
        svgLoader,
    },
    data() {
        return {
            refresh_disable: false,
            scroll_top: 0,
            isLoading: false,
            isRefresh: false,
            defaultImage: "",
            currentActiveIndex: 0,
            currentTag: [],
        };
    },
    beforeDestroy() {
        this.filters = cloneDeep(this.default_filters);
        this.$store.commit("libraryData/updateLibraryData", { posts: [], total: 0 });
    },
    computed: {
        posts() {
            return this.$store.state.libraryData.posts;
        },
        total() {
            return this.$store.state.libraryData.total;
        },
    },
    created() {
        this.filters = cloneDeep(this.default_filters);
        let library_server = this.$store.state.systemConfig.serverInfo.library_server;
        // let ajaxServer = library_server.protocol + library_server.addr + ":" + library_server.port + "/library/";
        let ajaxServer = libraryService.getBaseUrl();
        this.defaultImage = ajaxServer + "wp-content/themes/library_fliter/assets/img/default-thumbnail.png";
        this.iconPref = ajaxServer + "wp-content/plugins/wp_m_post_statistical/assets/image/";
    },
    mounted() {
        let scroll = this.$refs.main_post_content;
        if (scroll) {
            scroll.addEventListener("scroll", () => {
                this.scroll_top = scroll.scrollTop;
                if (this.scroll_top == 0) {
                    this.refresh_disable = false;
                } else {
                    this.refresh_disable = true;
                }
            });
        }
        this.isLoading = true;
        this.filters.isSearchLoading = true;
        setTimeout(async () => {
            if (this.$store.state.libraryData.library_token) {
                this.startRender(false);
            } else {
                let external_token = await this.getExternalToken();
                if (external_token) {
                    await this.getLibraryToken(external_token);
                    this.startRender(false);
                } else {
                    this.isLoading = false;
                }
            }
        }, 0);
    },
    methods: {
        onRefresh() {
            this.startRender(true);
        },
        handleSwipeChange(index) {
            this.currentActiveIndex = index;
        },
        startRender(reload) {
            let that = this;
            (async (reload) => {
                let function_list = [];
                if (that.getListDataFromStore("swipe_posts") && !reload) {
                    that.filters.swipe_posts = that.getListDataFromStore("swipe_posts");
                } else {
                    const fc_get_swipe_posts = async () => {
                        that.filters.swipe_posts = await that.getPostSlideImages();
                        that.$store.commit("libraryData/updateLibraryData", { swipe_posts: that.filters.swipe_posts });
                        return true;
                    };
                    function_list.push(fc_get_swipe_posts());
                }

                if (that.getListDataFromStore("tags") && !reload) {
                    that.filters.condition["tag"] = that.getListDataFromStore("tags");
                    that.filters.hot_types = that.getListDataFromStore("hot_tags");
                } else {
                    const fc_get_all_tags = async (resolve) => {
                        let tags = await that.getPostTags();
                        let parentTags = tags.filter(
                            (item) => item.meta && item.meta.is_parent && item.meta.is_parent[0] === "1"
                        );
                        let normalTags = tags.filter(
                            (item) => !(item.meta && item.meta.is_parent && item.meta.is_parent[0] === "1")
                        );
                        parentTags.forEach((item) => {
                            item.children = normalTags.filter(
                                (child) => child.meta && child.meta.parent_tag && child.meta.parent_tag[0] === item.name
                            );
                        });
                        that.filters.condition["tag"] = cloneDeep(tags);
                        let hot_tags = cloneDeep(parentTags).reduce((h, v) => {
                            const item = {
                                ...v,
                                cn: v.name,
                                en: v.meta && v.meta.tag_en ? v.meta.tag_en[0] : v.name,
                                key: "",
                                icon: v.meta && v.meta.tag_icon ? v.meta.tag_icon[0] : "",
                                sort_key: v.meta && v.meta.tag_sort ? parseInt(v.meta.tag_sort[0]) : 1000000,
                                is_active: "",
                            };
                            h.push(item);
                            return h;
                        }, []);
                        hot_tags.sort((a, b) => a.sort_key - b.sort_key);
                        hot_tags[0].is_active = "is_active";
                        that.filters.hot_types = hot_tags;
                        that.$store.commit("libraryData/updateLibraryData", { tags: normalTags, hot_tags });
                        return true;
                    };
                    function_list.push(fc_get_all_tags());
                }

                // if(this.getListDataFromStore('categories')&&!reload){
                //     this.filters.condition["category"] = this.getListDataFromStore('categories')
                // }else{
                //     this.filters.condition["category"] = await this.getAllCategories();
                //     this.$store.commit('libraryData/updateLibraryData',{categories:this.filters.condition["category"] })
                // }
                that.isRefresh = false;
                if (that.getListDataFromStore("init_posts") && !reload) {
                    let init_posts = that.getListDataFromStore("init_posts");
                    that.$store.commit("libraryData/updateLibraryData", {
                        posts: init_posts.posts,
                        total: init_posts.total,
                    });
                } else {
                    const fc_get_posts = async (resolve) => {
                        let new_hot_posts = await that.search(true);
                        that.$store.commit("libraryData/updateLibraryData", {
                            init_posts: { posts: that.posts, total: that.total },
                        });
                        return true;
                    };
                    function_list.push(fc_get_posts());
                }
                if (function_list.length > 0) {
                    const result = await Promise.all(function_list);
                }
                that.isLoading = false;
                that.filters.isSearchLoading = false;
            })(reload);
        },

        viewRepositoryPost(post) {
            this.$router.push(`/index/repository/post/${post.id}`);
        },
        focusInput() {
            this.$refs.search_input.focus();
            this.filters.focus_input = true;
        },
        async blurInput(flag) {
            this.filters.focus_input = false;
            await this.search(true);
        },
        async searchInput() {
            this.$refs.search_input.blur();
        },

        async loadMorePosts() {
            if (this.posts.length >= this.total && this.posts.length > 0) {
                this.filters.isSearchLoading = false;
                return;
            }
            if (!this.filters.isShowCondition) {
                await this.search(false);
            }
        },
        getPostImage(post) {
            let url = this.defaultImage;
            // if (post._embedded && post._embedded["wp:featuredmedia"]) {
            //     url = post._embedded["wp:featuredmedia"][0]?.source_url;
            // }

            if (post.post_more_details && post.post_more_details.post_thumbnail_url) {
                url = post.post_more_details.post_thumbnail_url;
            }

            return url;
        },

        async changeHotTypeByIndex(index) {
            //更改推荐后-选中是否处理
            if (this.filters.hot_types[index]) {
                if (!this.filters.hot_types[index].is_active) {
                    this.filters.hot_types.forEach((item, i) => {
                        item.is_active = "";
                        if (i == index) {
                            item.is_active = "is_active";
                            this.filters.condition["tag"] =
                                item.children.length > 0 ? item.children : this.getListDataFromStore("tags");
                        }
                        return item;
                    });
                } else {
                    this.filters.hot_types.forEach((item, i) => {
                        if (i == index) {
                            this.filters.condition["tag"] =
                                item.children.length > 0 ? item.children : this.getListDataFromStore("tags");
                        }
                        return item;
                    });
                }
            }
            await this.search(true);
            return;
        },
        get_hot_type(i, k, j, field = "") {
            let hot_type_index = (i - 1) * 8 + (k - 1) * 4 + j - 1;
            // console.error(this.filters.hot_types, hot_type_index);
            if (this.filters.hot_types[hot_type_index]) {
                if (field) {
                    if (field == "name") {
                        return window.vm.$store.state.language.currentLanguage == "EN"
                            ? this.filters.hot_types[hot_type_index]["en"]
                            : this.filters.hot_types[hot_type_index]["cn"];
                        // return this.getTagLibraryName(this.filters.hot_types[hot_type_index]);
                        // return this.lang[['key']];
                    } else if (field == "icon") {
                        const icon = this.filters.hot_types[hot_type_index][field];
                        return icon ? this.iconPref + icon : "";
                    } else {
                        return this.filters.hot_types[hot_type_index][field];
                    }
                } else {
                    return hot_type_index;
                }
            }
            return "";
        },
        getImage(post) {
            if (post._embedded && post._embedded["wp:featuredmedia"] && post._embedded["wp:featuredmedia"].length > 0) {
                return post._embedded["wp:featuredmedia"][0].source_url;
            }
            return "";
        },
        //显示过滤
        toggleCondition() {
            if (!this.filters.isSearchLoading) {
                this.filters.isShowCondition = !this.filters.isShowCondition;
            }
        },

        async handleback(newCondition, need_search) {
            this.$set(this.filters, "condition", newCondition);
            if (need_search) {
                let new_posts = await this.search(true);
            }
        },
        async handleReset() {
            let condition = {};
            this.filters.searchInputKey = "";
            for (let key in this.filters.condition) {
                let list = this.filters.condition[key];
                condition[key] = this.filters.condition[key].reduce((h, v) => {
                    v.is_checked = false;
                    h.push(v);
                    return h;
                }, []);
            }
            await this.handleback(condition, true);
        },
        async filedChange(field, parent, gparent) {
            var condition = cloneDeep(this.filters.condition);
            if (gparent) {
                condition[gparent] = condition[gparent] || {};
                condition[gparent][parent] = condition[gparent][parent] || [];
                condition[gparent][parent] = this.filters.condition[gparent][parent].reduce((h, v) => {
                    if (v.id == field.id) {
                        v.is_checked = !v.is_checked;
                    }
                    h.push(v);
                    return h;
                }, []);
            } else {
                condition[parent] = condition[parent] || [];
                condition[parent] = this.filters.condition[parent].reduce((h, v) => {
                    if (v.id == field.id) {
                        v.is_checked = !v.is_checked;
                    }
                    h.push(v);
                    return h;
                }, []);
            }
            await this.handleback(condition, true);
        },
        markTitle(title) {
            if (this.filters.searchInputKey) {
                const highlight = this.filters.searchInputKey;
                let replaceString = '<span style="color:#00c59d;">' + highlight + "</span>"; // 高亮替换v-html值
                return title.split(highlight).join(replaceString);
            } else {
                return title;
            }
        },
    },
};
</script>
<style>
.van-swipe__indicator--active {
    background-color: #00c59d;
}
</style>
<style lang="scss">
.library_repository_page {
    .post_thumbnail {
        width: 50px;
        height: 50px;
    }
    .repository_content {
        // flex: 1;
        margin: 0rem 0.25rem 0rem 0.25rem;
        height: 100%;
        overflow: auto;
        display: flex;
        flex-direction: column;
        .custom-indicator {
            position: relative;
            bottom: 5px;
            left: 0;
            right: 0;
            display: flex;
            justify-content: center;
            gap: 4px;
        }
        .line {
            width: 16px;
            height: 2px;
            background-color: #ebedf0;
            border-radius: 1px;
        }

        .line-active {
            background-color: rgb(0, 197, 157);
            border-radius: 1px;
            width: 16px;
        }
        .search_btn {
            height: 1.6rem;
            line-height: 1.6rem;
            text-align: center;
            background-color: rgba(0, 197, 157, 0.16);
            color: rgb(179, 179, 179);
            font-size: 0.7rem;
            border-radius: 0.7rem;
            background: #efefef;
            margin: 0.4rem 0rem 0.7rem 0rem;
            span,
            i {
                vertical-align: middle;
            }
            .van-cell {
                padding: 0;
                padding-top: 0.2rem;
            }
            .van-field__body {
                height: 1.5rem;
                color: #a7bcba;
                border-radius: 0.7rem;
                background: #f2f6f9;
                display: flex;
                align-items: center;
                justify-content: center;
                input {
                    text-align: center;
                }
                .icon_desc {
                    color: red;
                    line-height: 1.5rem;
                    color: #8b8b8b;
                    padding-top: 0.2rem;
                    padding-left: 0.5rem;
                }
                .svg_icon_search {
                    color: rgb(0, 197, 157);
                    float: center;
                    font-size: 0.7rem;
                    padding-top: 0.4rem;
                }
                .van-field__right-icon {
                    color: #969799;
                    width: 100%;
                    display: flex;
                    justify-content: center;
                }
            }
            .van-field__body:focus {
                .svg_icon_search {
                    display: none;
                }
            }
        }
        .type {
            flex: 2;
            overflow-y: auto;
            overflow-x: hidden;
            display: flex;
            flex-direction: column;
            margin-bottom: 0.5rem;
            height: 100%;
            .swipe_posts {
                height: 10rem;
                margin: 0rem 0rem 0.2rem 0rem;
                background-color: #373535;
                border-radius: 0.5rem;
                .van-swipe {
                    border-radius: 0.5rem;
                }
                .swipe_posts_item {
                    background: black;
                    width: 100%;
                    height: 10rem;
                    text-align: center;
                    img {
                        height: 100%;
                        text-align: center;
                    }
                }
            }
            .hot_type {
                display: flex;
                justify-content: space-between;
                flex-wrap: wrap;
                padding-top: 0.5rem;
                margin: 0.2rem 0rem 0.2rem 0rem;
                .row {
                    display: flex;
                    margin-bottom: 0.5rem;
                    justify-content: space-between;
                    flex-wrap: wrap;
                    flex-direction: row;
                    width: 100%;
                    .hot_type_item {
                        width: 25%;
                        display: flex;
                        justify-content: center;
                        flex-direction: column;
                        align-items: center;
                        font-size: 1.5rem;
                        color: #7a7a7a;
                        height: 2rem;
                        i {
                            font-size: 1.3rem;
                        }
                        .hot_type_name {
                            padding: 0 0.15rem; //   display:flex;
                            justify-content: center;
                            font-size: 0.6rem;
                            width: 5rem;
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            text-align: center;
                        }
                    }

                    .library_icon_img {
                        svg {
                            height: 29px;
                            width: 29px;
                        }
                    }
                    .is_active {
                        color: #00c59dc7;
                        svg {
                            color: #00c59dc7;
                            path {
                                fill: #00c59dc7;
                            }
                        }
                    }
                }
            }
            .filter_type {
                margin: 0 0rem 0 0rem;
                padding-top: 0.5rem;
                border-top: 0.01px solid #e3e3e3;
                display: flex;
                height: 1.5rem;
                .filter_type_list {
                    flex: 2;
                    overflow-y: hidden;
                    overflow-x: auto;
                    white-space: nowrap;
                    display: flex;
                    flex-direction: row;
                    .filter_type_item {
                        background-color: rgb(242, 246, 249);
                        color: rgb(155, 168, 172);
                        padding: 0.2rem;
                        margin: 0 0.2rem 0 0;
                        border-radius: 0.3rem;
                        font-size: 0.65rem;
                        height: 1.2rem;
                        display: flex;
                        justify-items: center;
                        align-items: center;
                    }
                    .is-active {
                        color: #00c59d;
                    }
                }
                .filter_type_icon {
                    width: 1.5rem;
                    text-align: right;
                    color: rgb(101, 109, 112);
                }
            }
            .result_type {
                display: flex;
                justify-content: space-between;
                margin: 0.5rem 0rem 0.3rem 0rem;
                height: 1rem;
                font-size: 0.7rem;
                .result_type_tips {
                    color: #c7cfd1;
                }
                .result_type_shuaxin {
                    color: #00c59d;
                    font-size: 0.7rem;
                    .icon_shuxin_filter {
                        padding-right: 0.2rem;
                        font-size: 0.7rem;
                    }
                }
            }

            .current_post {
                flex: 2;
                position: relative;
                // padding: 0.25rem 1.5rem 0.4rem 1.5rem;
                padding: 0.25rem 0rem 0.4rem 0rem;
                display: flex;
                flex-direction: row;
                flex-wrap: wrap;
                justify-content: space-between;
                .van-list__loading {
                    width: 100%;
                }

                .loading-mask {
                    margin-top: 0rem !important;
                }
                .current_post_empty {
                    color: #9ca8ad;
                    width: 100%;
                    height: 100%;
                    display: flex;
                    font-size: 0.65rem;
                    align-items: center;
                    flex-direction: column;
                    .empty-icon {
                        margin-top: 0.5rem;
                        margin-bottom: 0.8rem;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        height: 4rem;
                        width: 4rem;
                        border-radius: 1.9rem;
                        .icon {
                            font-size: 3rem;
                            color: #a2a2a2;
                        }
                    }
                }
                .current_post_item {
                    display: flex;
                    flex-direction: column;
                    width: 48%;
                    height: 11rem;
                    margin-bottom: 0.6rem;
                    align-items: center;
                    background: #ececec;
                    border-radius: 0.4rem;
                    .default_image {
                        width: 4rem;
                        height: 4rem;
                    }
                    .post_image {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        background: black;
                        border-radius: 0.4rem;
                        height: 7rem;
                        width: 100%;
                        img {
                            border-radius: 0.4rem;
                            text-align: center;
                            max-width: 100%;
                            max-height: 7rem;
                        }
                    }
                    .post_title {
                        width: calc(100% - 0.8rem);
                        text-align: left;
                        padding-left: 0.6rem;
                        padding-right: 0.8rem;
                        padding-top: 0.4rem;
                        font-size: 0.75rem;
                        height: 2.3rem;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        -webkit-line-clamp: 2;
                        display: -webkit-box;
                        -webkit-box-orient: vertical;
                    }
                    .post_like {
                        width: calc(100% - 0.8rem);
                        height: 1.5rem;
                        text-align: left;
                        font-size: 0.7rem;
                        padding-left: 0rem;
                        .post_like_item {
                            margin-right: 0.4rem;
                            color: #757575;
                            padding-right: 0.1rem;
                        }
                    }
                }
                .loading-mask.van_loading_spinner .van-loading__spinner {
                    opacity: 1;
                }
            }
            .no-more-text {
                color: #969799;
                font-size: 14px;
                line-height: 50px;
                text-align: center;
                /* top: 2rem; */
                margin-top: -1.5rem;
            }
        }
    }
}
</style>
