<template>
    <div class="login_container">
        <div v-if="isCookiesNotificationShow"><cookies-notification /></div>
        <div class="login_card">
            <div  class="app_operate_btns clearfix" @mousedown="moveWindowStart">
                <i v-show="isCef" class="fr iconfont iconsearchclose" @click="closeApp"></i>
                <i v-show="isCef" class="fr iconfont iconmin" @click="minApp"></i>
                <div class="language_select fr">
                    <el-select v-model="curLanguage" @change="changeLang">
                        <el-option
                            v-for="item in languageOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        >
                        </el-option>
                    </el-select>
                </div>
                <!-- <i v-show="isWorkStation" class="icon iconfont iconout fr"  @click="returnApp"></i> -->
            </div>
            <div class="login_card_content">
                <div class="notify-bar"><notifyBar></notifyBar></div>
                <login-form
                    element-loading-text=""
                    element-loading-background="#fff"
                    ref="loginForm"
                ></login-form>
                <div class="register_version">{{ register_version }}</div>
                <router-view></router-view>
            </div>
        </div>

        <!--  <div class="container">
            <div class="register_box">
                <div class="reg_slogan clearfix">
                    <a class="reg_slogan_login fl" :class='{action:form_type==1}' @click="form_type=1">{{lang.login_title}}</a>
                    <a @click="form_type=2" class="reg_slogan_register fl" :class='{action:form_type==2}'>{{lang.register_title}}</a>
                </div>

            </div>

        </div> -->
        <!-- <forget-password-form></forget-password-form> -->
        <a
            target="_blank"
            href="https://beian.miit.gov.cn/#/Integrated/index"
            class="licence"
            v-if="!globalParams.isCef && !globalParams.isCE"
            >粤ICP备05083646号-4</a
        >
    </div>
</template>
<script>
import languages from '@/common/language'
import service from "../service/service";
import base from "../lib/base";
import appOperateTool from "../lib/appOperateTool";
import Tool from "@/common/tool.js";
import CWorkstationCommunicationMng from "@/common/CommunicationMng/index";
import {
    parseImageListToLocal,
    destroyAllConference,
    getLiveRoomObj,
    resetGlobalCustomWindowObject,
} from "../lib/common_base";
import LoginForm from "../components/loginForm.vue";
import { resetStoreState } from "../store/index";
import cookiesNotification from "../components/cookiesNotification.vue";
import notifyBar from "../components/notifyBar.vue";
export default {
    mixins: [base, appOperateTool],
    name: "Login_Pc",
    components: {
        cookiesNotification,
        LoginForm,
        notifyBar,
    },
    data() {
        return {
            register_version: "",
            form_type: 1, //表单类型，1登录2注册
            isCookiesNotificationShow: false,
            curLanguage: '',
            languageOptions: [
                {
                    value: 'CN',
                    label: "简体中文",
                },
                {
                    value: 'EN',
                    label: "English",
                },
                {
                    value: 'ES',
                    label: "Español",
                },
                {
                    value: 'PTBR',
                    label: "Português",
                },
                {
                    value: 'RU',
                    label: "Русский язык",
                },
                {
                    value: 'DE',
                    label: "Deutsch",
                },
                {
                    value: 'FR',
                    label: "Français",
                },
                {
                    value: 'IT',
                    label: "Italiano",
                },
            ],
        };
    },
    beforeCreate() {
        //改变窗口大小
        CWorkstationCommunicationMng.ResizeBrowser({ w: 320, h: 435 });
        if (window.vm) {
            destroyAllConference();
            resetStoreState();
            window.vm.$root.resetCustomRootObject();
            window.vm.$root.eventBus.$emit("cancelDownLoadTask");
        }
        resetGlobalCustomWindowObject();
    },
    created() {
        console.log('lang',this.lang.currentLanguage)
        this.curLanguage=this.lang.currentLanguage;
    },
    mounted() {
        this.$nextTick(() => {
            var that = this;
            console.error('this.isInternalNetworkEnv',this.isInternalNetworkEnv)
            if (this.isInternalNetworkEnv) {
                this.$refs.loginForm.changeLoginType(3);
            } else {
                this.$refs.loginForm.changeLoginType(4);
            }
            this.isCookiesNotificationShow = Tool.checkAppClient("Browser");
            this.$root.eventBus.$off("updateVersion").$on("updateVersion", (data) => {
                //app获取版本号后更新数据
                this.register_version = data.REGISTER_VERSION;
            });
            CWorkstationCommunicationMng.GetDeviceID({});
            CWorkstationCommunicationMng.EnterLoginPage();
        });
    },
    destroyed() {
        CWorkstationCommunicationMng.ExitLoginPage();
    },
    computed: {
        isMaxScreen() {
            return this.globalParams.isMaxScreen;
        },
        isWorkStation() {
            return this.isCef && Tool.ifAppWorkstationClientType(this.systemConfig.clientType);
        },
        serverInfo() {
            return this.systemConfig.serverInfo;
        },
        isInternalNetworkEnv(){
            return this.systemConfig.serverInfo.network_environment
        },
    },
    methods: {
        resetStatus() {},
        changeLang(lang){
            if(this.isCef){
                const params = {
                    language: lang
                }
                Tool.createCWorkstationCommunicationMng({
                    name: "RequestSetClientConfInfo",
                    emitName: "NotifyRequestSetClientConfInfo",
                    params,
                    timeout: 5000,
                }).then((res) => {
                    if(res.error){
                        console.error('RequestSetClientConfInfo error')
                    }

                    this.$MessageBox.confirm(languages[lang].effect_after_restart, languages[lang].tip_title, {
                        confirmButtonText: languages[lang].confirm_txt,
                        cancelButtonText: languages[lang].cancel_btn,
                        callback: async (action) => {
                            if (action === "confirm") {}
                        },
                    });

                })
            } else{
                window.localStorage.setItem('lang',lang)
    		    this.$store.commit('language/setLanguage', languages[lang]);
                this.$store.commit('language/setLanguage', { currentLanguage: lang })
            }

        }
    },
};
</script>
<style lang="scss">
.login_container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
    background-color: #23b18b;
    user-select: none;
    display: flex;
    align-items: center;
    justify-content: center;
    & ~ .el-message {
        min-width: 300px;
    }
    .login_card {
        width: 320px;
        height: 435px;
        position: relative;
        background: #fff;
        box-shadow: 0 0 2px rgba(0 0 0 / 20%);
        display: flex;
        flex-direction: column;
        .app_operate_btns {
            position: absolute;
            right: 0;
            left: 50px;
            top: 0;
            height: 30px;
            z-index: 3;
            cursor: pointer;
            .language_select {
                margin-right: 20px;
                margin-top: 5px;
                .el-select {
                    width: 120px;
                    .el-input > input {
                        height: 30px;
                        padding: 5px 8px;
                        font-size: 13px;
                        color: #999;
                    }
                    .el-input__suffix {
                        top: -2px;
                        right: 0px;
                        i {
                            font-weight: 900;
                        }
                    }
                }
            }
            i {
                margin-top: 12px;
                width: 10px;
                height: 10px;
                fill: #666;
                margin-right: 16px;
                cursor: pointer;
            }
            i {
                font-size: 12px;
                color: #999;
                line-height: 1;
                cursor: pointer;
                margin-right: 16px;
            }
        }
        .login_card_content {
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            display: flex;
            flex-direction: column;
            flex: 1;
            .el-input > input {
                padding: 12px;
                font-size: 12px;
                background: #e6e6e6;
                color: #666;
                box-sizing: border-box;
                letter-spacing: 2px;
                font-weight: 500;
                line-height: 12px;
                height: 34px;
                box-shadow: inset 1px 1px 2px rgba(0, 0, 0, 0.1);
                -webkit-appearance: none;
                appearance: none;
                border-radius: 5px;
                outline: none;
                resize: none;
                border: none;
                display: block;
                -webkit-box-shadow: 0 0 0px 1000px #e6e6e6 inset;
            }

            input:-webkit-autofill {
                transition: background-color 999999s ease-in-out 0s;
                -webkit-text-fill-color: #666;
            }

            .el-input {
                display: block;
                margin-bottom: 12px;
            }
        }
        .register_version {
            position: absolute;
            right: 6px;
            bottom: 0;
            color: #999;
            font-size: 14px;
        }
    }
    .licence {
        position: absolute;
        right: 10px;
        bottom: 0;
        font-size: 14px;
        color: #fff;
        z-index: 2;
    }

    .el-form-item {
        margin-bottom: 0px;
    }
    .el-form-item__error {
        padding-top: 1px;
    }
    .el-checkbox__label {
        font-size: 12px;
        color: #b3b3b3;
        padding-left: 12px;
    }
    .common_btn {
        font-size: 14px;
        line-height: 1;
        background: #51c1a5;
        color: #c4ece3;
        text-align: center;
        display: block;
        padding: 12px 0;
        border-radius: 6px;
        cursor: pointer;
    }
    .ban_btn {
        background: rgba(81, 193, 165, 0.8);
        pointer-events: none;
        cursor: not-allowed;
    }
    .el-checkbox .el-checkbox__input.is-checked .el-checkbox__inner {
        background-color: #51c1a5;
        border-color: #51c1a5;
    }
    .mobile_field {
        input {
            padding-left: 90px !important;
        }
    }
    & ~ .el-message-box__wrapper .el-message-box {
        width: 300px;
    }
    .notify-bar {
        flex-shrink: 0;
        position: absolute;
        left: 50px;
        right: 50px;
        top: 20px;
        z-index: 20;
    }
}
</style>
